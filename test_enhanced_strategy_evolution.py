#!/usr/bin/env python3
"""
Test Script for Enhanced Strategy Evolution Agent

This script demonstrates the enhanced strategy evolution agent capabilities:
1. Loading base strategies from strategies.yaml
2. Running multi-objective optimization
3. Generating stock-specific strategy variants
4. Updating strategies.yaml with enhanced strategies
5. Managing strategy lifecycle
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

from agents.enhanced_strategy_evolution_agent import EnhancedStrategyEvolutionAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_strategy_loading():
    """Test loading base strategies"""
    logger.info("🧪 Testing strategy loading...")
    
    agent = EnhancedStrategyEvolutionAgent()
    success = await agent.load_base_strategies()
    
    if success:
        logger.info(f"✅ Successfully loaded {len(agent.base_strategies)} base strategies")
        for strategy in agent.base_strategies[:3]:  # Show first 3
            logger.info(f"  - {strategy['name']}")
    else:
        logger.error("❌ Failed to load base strategies")
    
    return success

async def test_stock_discovery():
    """Test stock universe discovery"""
    logger.info("🧪 Testing stock universe discovery...")
    
    agent = EnhancedStrategyEvolutionAgent()
    stocks = await agent.discover_stock_universe()
    
    if stocks:
        logger.info(f"✅ Discovered {len(stocks)} stocks in universe")
        logger.info(f"  Sample stocks: {stocks[:5]}")
    else:
        logger.warning("⚠️ No stocks discovered, using default list")
        stocks = ["TCS", "INFY", "RELIANCE", "HDFCBANK", "ICICIBANK"]
        logger.info(f"  Default stocks: {stocks}")
    
    return stocks

async def test_fitness_evaluation():
    """Test strategy fitness evaluation"""
    logger.info("🧪 Testing strategy fitness evaluation...")
    
    try:
        agent = EnhancedStrategyEvolutionAgent()
        
        # Load base strategies
        await agent.load_base_strategies()
        
        if not agent.base_strategies:
            logger.error("❌ No base strategies loaded for fitness evaluation")
            return False
        
        # Create a test variant from the first base strategy
        from agents.enhanced_strategy_evolution_agent import StrategyVariant, StrategyStatus
        import uuid
        
        base_strategy = agent.base_strategies[0]
        test_variant = StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_strategy['name'],
            stock_name="TCS",
            timeframe="1min",
            ranking=100,
            entry_conditions=base_strategy.get('entry', {}),
            exit_conditions=base_strategy.get('exit', {}),
            intraday_rules=base_strategy.get('intraday_rules', {}),
            risk_reward_ratios=base_strategy.get('risk_reward_ratios', [[1, 2]]),
            risk_management=base_strategy.get('risk_management', {}),
            position_sizing=base_strategy.get('position_sizing', {}),
            status=StrategyStatus.CANDIDATE
        )
        
        # Evaluate fitness (this will use mock data if real backtesting fails)
        fitness_metrics = await agent.evaluate_strategy_fitness(test_variant)
        
        logger.info("✅ Fitness evaluation completed")
        logger.info(f"  Composite Score: {fitness_metrics.get('composite_score', 0.0):.4f}")
        logger.info(f"  Sharpe Ratio: {fitness_metrics.get('sharpe_ratio', 0.0):.4f}")
        logger.info(f"  Max Drawdown: {fitness_metrics.get('max_drawdown', 0.0):.2f}%")
        logger.info(f"  Win Rate: {fitness_metrics.get('win_rate', 0.0):.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Fitness evaluation failed: {e}")
        return False

async def test_database_operations():
    """Test database operations"""
    logger.info("🧪 Testing database operations...")
    
    try:
        agent = EnhancedStrategyEvolutionAgent()
        
        # Test loading variants (should be empty initially)
        variants = await agent.load_variants_from_database()
        logger.info(f"✅ Loaded {len(variants)} variants from database")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database operations failed: {e}")
        return False

async def test_strategy_enhancement():
    """Test the main strategy enhancement process"""
    logger.info("🧪 Testing strategy enhancement process...")
    
    try:
        agent = EnhancedStrategyEvolutionAgent()
        
        # Run a limited enhancement process (just 1-2 strategies for testing)
        logger.info("🚀 Starting limited strategy enhancement...")
        
        # Load base strategies
        if not await agent.load_base_strategies():
            logger.error("❌ Failed to load base strategies")
            return False
        
        # Limit to first 2 strategies for testing
        agent.base_strategies = agent.base_strategies[:2]
        
        # Run enhancement
        success = await agent.enhance_strategies_yaml()
        
        if success:
            logger.info("✅ Strategy enhancement completed successfully")
        else:
            logger.warning("⚠️ Strategy enhancement completed with warnings")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Strategy enhancement failed: {e}")
        return False

async def run_comprehensive_test():
    """Run comprehensive test of all components"""
    logger.info("🧬 Starting Comprehensive Enhanced Strategy Evolution Agent Test")
    logger.info("=" * 80)
    
    test_results = {}
    
    # Test 1: Strategy Loading
    test_results['strategy_loading'] = await test_strategy_loading()
    
    # Test 2: Stock Discovery
    stocks = await test_stock_discovery()
    test_results['stock_discovery'] = len(stocks) > 0
    
    # Test 3: Database Operations
    test_results['database_operations'] = await test_database_operations()
    
    # Test 4: Fitness Evaluation
    test_results['fitness_evaluation'] = await test_fitness_evaluation()
    
    # Test 5: Strategy Enhancement (main functionality)
    test_results['strategy_enhancement'] = await test_strategy_enhancement()
    
    # Summary
    logger.info("=" * 80)
    logger.info("🏁 Test Summary:")
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed! Enhanced Strategy Evolution Agent is working correctly.")
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} tests failed. Check the logs for details.")
    
    return passed_tests == total_tests

async def run_single_enhancement():
    """Run a single strategy enhancement cycle"""
    logger.info("🚀 Running Single Strategy Enhancement Cycle")
    logger.info("=" * 60)
    
    try:
        agent = EnhancedStrategyEvolutionAgent()
        
        # Run enhancement
        success = await agent.enhance_strategies_yaml()
        
        if success:
            logger.info("✅ Single enhancement cycle completed successfully")
            logger.info("📋 Check config/strategies.yaml for enhanced strategies")
            logger.info("📊 Check data/evolved_strategies.db for detailed variant data")
        else:
            logger.error("❌ Single enhancement cycle failed")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error in single enhancement: {e}")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Enhanced Strategy Evolution Agent")
    parser.add_argument("--mode", choices=["test", "enhance"], default="test",
                       help="Mode: 'test' for comprehensive testing, 'enhance' for single enhancement")
    
    args = parser.parse_args()
    
    if args.mode == "test":
        success = asyncio.run(run_comprehensive_test())
    else:
        success = asyncio.run(run_single_enhancement())
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
