strategies:
- entry:
    long: rsi_14 < 30 and close > ema_10
    short: rsi_14 > 70 and close < ema_10
  exit:
    long: rsi_14 > 60 or close < ema_10
    short: rsi_14 < 40 or close > ema_10
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: RSI_Reversal
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: macd > macd_signal and close > ema_20
    short: macd < macd_signal and close < ema_20
  exit:
    long: macd < macd_signal or close < ema_20
    short: macd > macd_signal or close > ema_20
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: MACD_Crossover
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: ema_5 > ema_13 and ema_13 > ema_21 and close > ema_5
    short: ema_5 < ema_13 and ema_13 < ema_21 and close < ema_5
  exit:
    long: ema_5 < ema_13 or close < ema_5
    short: ema_5 > ema_13 or close > ema_5
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: EMA_Stacking
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: close < bb_lower and rsi_5 < 40
    short: close > bb_upper and rsi_5 > 60
  exit:
    long: close > bb_upper or rsi_5 > 70
    short: close < bb_lower or rsi_5 < 30
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: Bollinger_Bounce
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: close < vwap and rsi_14 < 40 and close > ema_10
    short: close > vwap and rsi_14 > 60 and close < ema_10
  exit:
    long: close > vwap or rsi_14 > 60
    short: close < vwap or rsi_14 < 40
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: VWAP_Bounce
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: close >= donchian_high and volume > 1000
    short: close <= donchian_low and volume > 1000
  exit:
    long: close <= donchian_low
    short: close >= donchian_high
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: Donchian_Break
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: close > supertrend and rsi_5 > 50
    short: close < supertrend and rsi_5 < 50
  exit:
    long: close < supertrend
    short: close > supertrend
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: Supertrend_Trend
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: close > cpr_bottom and close < cpr_top and rsi_14 < 50
    short: close < cpr_top and close > cpr_bottom and rsi_14 > 50
  exit:
    long: close > cpr_top or close < cpr_bottom
    short: close < cpr_bottom or close > cpr_top
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: CPR_Bounce
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: adx > 25 and close > ema_20
    short: adx > 25 and close < ema_20
  exit:
    long: adx < 20 or close < ema_20
    short: adx < 20 or close > ema_20
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: ADX_Momentum
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: vcp_pattern == 1 and upward_candle == 1 and volume > 1000 and rsi_14 > 50
    short: vcp_pattern == 1 and downward_candle == 1 and volume > 1000 and rsi_14
      < 50
  exit:
    long: vcp_pattern == 0 or rsi_14 < 40
    short: vcp_pattern == 0 or rsi_14 > 60
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: VCP_Breakout
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: ema_5 > ema_10 and ema_10 > ema_20 and rsi_14 > 50 and close > vwap
    short: ema_5 < ema_10 and ema_10 < ema_20 and rsi_14 < 50 and close < vwap
  exit:
    long: ema_5 < ema_10 or rsi_14 < 30
    short: ema_5 > ema_10 or rsi_14 > 70
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: EMA_RSI_Combo
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
- entry:
    long: close > ema_20 and volume > 2000 and rsi_5 > 60
    short: close < ema_20 and volume > 2000 and rsi_5 < 40
  exit:
    long: close < ema_20 or rsi_5 < 40
    short: close > ema_20 or rsi_5 > 60
  intraday_rules:
    exit_all_at: '15:10'
    no_trade_after: '14:30'
  name: Volume_Breakout
  position_sizing:
    max_capital_multiplier: 3.5
    max_qty_formula: risk_per_trade / stock_price
    type: dynamic_risk_based
  ranking: 100
  risk_management:
    stop_loss_type: percentage
    stop_loss_value: 0.01
    take_profit_type: percentage
    take_profit_value: 0.02
  risk_reward_ratios:
  - - 1
    - 2
  - - 1.5
    - 2
  - - 1
    - 3
  stock_name: ''
  timeframe:
  - 1min
  - 3min
  - 5min
  - 15min
