# ═══════════════════════════════════════════════════════════════════════════════
# 🧬 ENHANCED STRATEGY EVOLUTION AGENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Configuration for the Enhanced Strategy Evolution Agent that addresses all
# enhancement points from the error.txt file

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 EVOLUTION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
evolution:
  # Population parameters
  population_size: 50
  elite_size: 10
  max_generations: 100
  
  # Multi-objective optimization (Enhancement Point #3)
  objectives:
    - name: "sharpe_ratio"
      weight: 0.4
      direction: "maximize"
      target_value: 1.5
    - name: "max_drawdown"
      weight: 0.3
      direction: "minimize"
      target_value: 10.0
    - name: "win_rate"
      weight: 0.3
      direction: "maximize"
      target_value: 0.6
  
  # Strategy enhancement parameters
  max_variants_per_strategy: 5        # Maximum variants per base strategy
  min_ranking_threshold: 70           # Minimum ranking (0-100) to keep variant
  
  # Stock selection criteria
  stock_selection_criteria:
    min_volume: 1000000               # Minimum daily volume
    min_price: 10.0                   # Minimum stock price
    max_price: 5000.0                 # Maximum stock price
    sectors: ["all"]                  # Sectors to include (or "all")
    max_stocks_per_strategy: 5        # Maximum stocks to test per strategy
  
  # Backtesting integration (Enhancement Point #1)
  backtesting_config:
    max_symbols: 10                   # Maximum symbols for backtesting
    max_files: 50                     # Maximum files to process
    ranking_threshold: 0              # Minimum ranking for backtesting
    enable_caching: true              # Enable backtesting result caching
    cache_duration_hours: 24          # Cache duration in hours

# ═══════════════════════════════════════════════════════════════════════════════
# 🏪 STORAGE CONFIGURATION (Enhancement Point #7)
# ═══════════════════════════════════════════════════════════════════════════════
storage:
  # Database configuration (separate from YAML config)
  database_path: "data/evolved_strategies.db"
  backup_interval_hours: 24
  max_backup_files: 10
  
  # File storage paths
  strategies_dir: "data/evolved_strategies"
  performance_dir: "data/evolution_performance"
  backup_dir: "data/evolution_backups"
  
  # Data retention policies
  retention:
    performance_history_days: 365     # Keep performance history for 1 year
    failed_strategies_days: 30        # Keep failed strategies for 30 days
    backup_files_days: 90             # Keep backup files for 90 days

# ═══════════════════════════════════════════════════════════════════════════════
# 🌊 MARKET REGIME ADAPTATION (Enhancement Point #5)
# ═══════════════════════════════════════════════════════════════════════════════
market_regime:
  enable_adaptation: true
  
  # Regime detection parameters
  detection_window_days: 30           # Days to analyze for regime detection
  volatility_threshold: 0.02          # Volatility threshold for regime classification
  trend_threshold: 0.05               # Trend threshold for regime classification
  
  # Learned adaptations (instead of hardcoded)
  adaptation_learning:
    enable_learning: true
    learning_rate: 0.01
    adaptation_memory_days: 90        # Days to remember adaptations
    min_samples_for_learning: 100     # Minimum samples to learn adaptations
  
  # Regime-specific parameters
  regimes:
    bullish:
      risk_multiplier_range: [0.8, 1.2]
      stop_loss_adjustment_range: [0.9, 1.1]
      take_profit_adjustment_range: [1.0, 1.3]
    bearish:
      risk_multiplier_range: [0.6, 1.0]
      stop_loss_adjustment_range: [0.8, 1.0]
      take_profit_adjustment_range: [0.8, 1.2]
    sideways:
      risk_multiplier_range: [0.7, 1.1]
      stop_loss_adjustment_range: [0.9, 1.1]
      take_profit_adjustment_range: [0.9, 1.2]
    volatile:
      risk_multiplier_range: [0.5, 0.9]
      stop_loss_adjustment_range: [1.1, 1.4]
      take_profit_adjustment_range: [1.2, 1.8]

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 STRATEGY LIFECYCLE MANAGEMENT (Enhancement Point #9)
# ═══════════════════════════════════════════════════════════════════════════════
lifecycle:
  # Status thresholds (based on composite fitness score 0-1)
  status_thresholds:
    champion: 0.8                     # Promote to champion
    challenger: 0.6                   # Promote to challenger
    testing: 0.4                      # Keep in testing
    candidate: 0.2                    # Keep as candidate
    failed: 0.0                       # Mark as failed
  
  # Promotion/demotion rules
  promotion_rules:
    min_evaluation_period_days: 7     # Minimum days before promotion
    min_trades_for_promotion: 10      # Minimum trades before promotion
    consecutive_good_days: 3          # Consecutive good days for promotion
  
  demotion_rules:
    max_consecutive_losses: 5         # Max consecutive losses before demotion
    max_drawdown_for_demotion: 15.0   # Max drawdown % for demotion
    min_sharpe_for_champion: 1.0      # Min Sharpe ratio to remain champion
  
  # A/B testing configuration
  ab_testing:
    enable_ab_testing: true
    test_duration_days: 14            # Duration of A/B tests
    min_sample_size: 50               # Minimum sample size for statistical significance
    confidence_level: 0.95            # Statistical confidence level

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 ENHANCED MONITORING (Enhancement Point #10)
# ═══════════════════════════════════════════════════════════════════════════════
monitoring:
  # Structured logging
  logging:
    level: "INFO"
    format: "json"                    # JSON format for structured logging
    file: "logs/enhanced_strategy_evolution.log"
    max_file_size_mb: 100
    backup_count: 5
  
  # Real-time monitoring
  real_time:
    enable_monitoring: true
    update_interval_seconds: 60       # Update interval for real-time metrics
    
    # Metrics to track
    metrics:
      - "generation_progress"
      - "fitness_scores"
      - "strategy_performance"
      - "agent_health"
      - "evolution_convergence"
  
  # Alerting
  alerts:
    enable_alerts: true
    
    # Alert conditions
    conditions:
      - metric: "avg_fitness_decline"
        threshold: 0.1
        duration_minutes: 30
      - metric: "failed_strategies_ratio"
        threshold: 0.5
        duration_minutes: 60
      - metric: "evolution_stagnation"
        threshold: 10                 # Generations without improvement
        duration_minutes: 120

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 OPTIMIZATION PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
optimization:
  # Optuna configuration
  optuna:
    n_trials: 20                      # Number of trials per optimization
    timeout_seconds: 3600             # Timeout per optimization
    n_jobs: 1                         # Parallel jobs (set to 1 for stability)
    
    # Sampler configuration
    sampler: "TPE"                    # Tree-structured Parzen Estimator
    pruner: "MedianPruner"            # Median pruning for early stopping
  
  # Parameter ranges for optimization
  parameter_ranges:
    risk_pct: [0.5, 3.0]             # Risk percentage range
    reward_pct: [1.0, 5.0]           # Reward percentage range
    stop_loss: [0.005, 0.03]         # Stop loss range
    rsi_period: [10, 21]             # RSI period range
    ema_period: [5, 50]              # EMA period range
    volume_multiplier: [1.2, 3.0]    # Volume multiplier range

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 PERFORMANCE OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  # Parallel processing
  parallel_processing:
    enable_multiprocessing: true
    max_workers: 4                    # Maximum worker processes
    chunk_size: 10                    # Chunk size for parallel processing
  
  # Memory management
  memory:
    max_memory_usage_gb: 8            # Maximum memory usage
    enable_garbage_collection: true   # Enable aggressive garbage collection
    gc_interval_seconds: 300          # Garbage collection interval
  
  # Caching
  caching:
    enable_result_caching: true
    cache_size_mb: 500               # Maximum cache size
    cache_ttl_hours: 24              # Cache time-to-live

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DEVELOPMENT AND TESTING
# ═══════════════════════════════════════════════════════════════════════════════
development:
  # Testing mode
  enable_testing_mode: false
  test_data_path: "data/test/sample_data.parquet"
  
  # Debug settings
  debug:
    enable_debug_logging: false
    save_intermediate_results: false
    enable_profiling: false
    profiling_output_dir: "data/profiling"
  
  # Simulation settings
  simulation:
    enable_simulation: false
    simulation_speed_multiplier: 10   # Speed up simulation
    mock_backtesting: false          # Use mock backtesting for testing

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 INTEGRATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
integration:
  # Agent communication
  agents:
    signal_agent:
      enabled: true
      config_path: "config/strategies.yaml"
    backtesting_agent:
      enabled: true
      function_name: "run_backtesting_for_evolution"
  
  # External services
  external_services:
    enable_webhook_notifications: false
    webhook_url: ""
    
    enable_database_sync: false
    sync_interval_hours: 6
