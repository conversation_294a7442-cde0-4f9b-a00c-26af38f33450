# Enhanced Strategy Evolution Agent

## Overview

The Enhanced Strategy Evolution Agent is a comprehensive strategy optimization system that addresses all the enhancement points identified in the error.txt file. It implements advanced evolutionary algorithms, multi-objective optimization, and intelligent strategy management to continuously improve trading strategies.

## Key Features

### 🧬 1. Full Backtesting Integration
- **Uses existing backtesting agent** for fitness evaluation
- **Real historical data** instead of synthetic/heuristic evaluation
- **Efficient data caching** to avoid redundant computations
- **Comprehensive performance metrics** calculation

### 🔄 2. Multi-Objective Optimization
- **True Pareto-optimal solutions** using Optuna
- **Multiple objectives**: Sharpe ratio, max drawdown, win rate
- **Configurable objective weights** and directions
- **Advanced parameter optimization** with TPE sampler

### 🎯 3. Stock-Specific Strategy Variants
- **Enhances strategies.yaml** with best-performing stock variants
- **Ranking system (0-100)** for strategy prioritization
- **Multiple risk/reward ratios** per strategy
- **Timeframe-specific optimization**

### 📊 4. Polars-Based Data Processing
- **High-performance data operations** using Polars
- **Memory-efficient processing** for large datasets
- **GPU acceleration support** where available
- **Vectorized computations** for speed

### 🏪 5. Dedicated Strategy Storage
- **SQLite database** separate from YAML config
- **Performance history tracking**
- **Evolution history logging**
- **Automatic backup management**

### 🌊 6. Market Regime Adaptation
- **Learned adaptations** instead of hardcoded rules
- **Regime-specific parameter optimization**
- **Adaptive learning** based on market conditions
- **Historical regime analysis**

### 🔄 7. Strategy Lifecycle Management
- **Promotion/demotion logic** based on performance
- **Status tracking**: Candidate → Testing → Challenger → Champion
- **A/B testing framework** for strategy validation
- **Graceful retirement** of underperforming strategies

### 📈 8. Enhanced Monitoring
- **Structured JSON logging** for easy parsing
- **Real-time performance tracking**
- **Alert system** for performance degradation
- **Comprehensive metrics dashboard**

## Architecture

```
Enhanced Strategy Evolution Agent
├── Core Evolution Logic
│   ├── Multi-objective optimization (Optuna)
│   ├── Strategy variant generation
│   └── Fitness evaluation (via backtesting agent)
├── Strategy Management
│   ├── Lifecycle management
│   ├── Performance tracking
│   └── Database storage
├── Market Regime Adaptation
│   ├── Regime detection
│   ├── Learned adaptations
│   └── Parameter optimization
└── Integration Layer
    ├── Signal Agent integration
    ├── Backtesting Agent integration
    └── YAML config management
```

## Installation and Setup

### Prerequisites
```bash
# Install required packages
pip install polars optuna pyyaml sqlite3 numpy

# Ensure existing agents are available
# - agents/signal_agent.py
# - agents/enhanced_backtesting_kimi.py
```

### Configuration
1. Copy `config/enhanced_strategy_evolution_config.yaml` to your config directory
2. Adjust parameters as needed:
   - Population size and generations
   - Optimization objectives and weights
   - Stock selection criteria
   - Storage paths

### Database Setup
The agent automatically creates the SQLite database on first run:
- `data/evolved_strategies.db` - Main strategy storage
- Tables: strategy_variants, performance_history, evolution_history

## Usage

### Basic Usage
```python
from agents.enhanced_strategy_evolution_agent import EnhancedStrategyEvolutionAgent

# Initialize agent
agent = EnhancedStrategyEvolutionAgent()

# Run strategy enhancement
success = await agent.enhance_strategies_yaml()
```

### Command Line Testing
```bash
# Run comprehensive tests
python test_enhanced_strategy_evolution.py --mode test

# Run single enhancement cycle
python test_enhanced_strategy_evolution.py --mode enhance
```

### Integration with Existing System
```python
# Use with existing backtesting agent
from agents.enhanced_backtesting_kimi import run_backtesting_for_evolution

# Use with existing signal agent
from agents.signal_agent import SignalAgent
```

## Configuration Options

### Evolution Parameters
```yaml
evolution:
  population_size: 50
  max_generations: 100
  objectives:
    - name: "sharpe_ratio"
      weight: 0.4
      direction: "maximize"
```

### Stock Selection
```yaml
stock_selection_criteria:
  min_volume: 1000000
  min_price: 10.0
  max_price: 5000.0
  max_stocks_per_strategy: 5
```

### Storage Configuration
```yaml
storage:
  database_path: "data/evolved_strategies.db"
  backup_interval_hours: 24
  max_backup_files: 10
```

## Output Format

### Enhanced strategies.yaml
The agent enhances the existing strategies.yaml with stock-specific variants:

```yaml
strategies:
  - name: CPR_Bounce_TCS_1min
    ranking: 94
    timeframe: ["1min"]
    stock_name: "TCS"
    entry:
      long: close > cpr_bottom and close < cpr_top and rsi_14 < 50
      short: close < cpr_top and close > cpr_bottom and rsi_14 > 50
    exit:
      long: close > cpr_top or close < cpr_bottom
      short: close < cpr_bottom or close > cpr_top
    risk_reward_ratios:
      - [1.3, 2.7]
    risk_management:
      stop_loss_type: "percentage"
      stop_loss_value: 0.02
      take_profit_type: "percentage"
      take_profit_value: 0.054  # Calculated from risk/reward ratio
```

### Database Storage
- **strategy_variants**: Complete variant definitions
- **performance_history**: Historical performance metrics
- **evolution_history**: Generation-by-generation evolution progress

## Performance Optimizations

### Memory Management
- Efficient Polars operations
- Automatic garbage collection
- Configurable memory limits

### Parallel Processing
- Multi-process optimization
- Concurrent fitness evaluation
- Batch processing for large datasets

### Caching
- Backtesting result caching
- Performance metric caching
- Configurable cache TTL

## Monitoring and Alerts

### Structured Logging
```json
{
  "timestamp": "2025-01-23T10:30:00Z",
  "level": "INFO",
  "component": "strategy_evolution",
  "event": "fitness_evaluation",
  "strategy_id": "uuid-123",
  "metrics": {
    "sharpe_ratio": 1.45,
    "max_drawdown": 8.2,
    "composite_score": 0.78
  }
}
```

### Real-time Metrics
- Generation progress
- Fitness score distributions
- Strategy performance trends
- System health indicators

## Troubleshooting

### Common Issues
1. **No strategies loaded**: Check `config/strategies.yaml` exists
2. **No stocks discovered**: Ensure `data/features/` contains stock data
3. **Backtesting failures**: Verify backtesting agent is properly configured
4. **Database errors**: Check write permissions for `data/` directory

### Debug Mode
Enable debug logging in configuration:
```yaml
development:
  debug:
    enable_debug_logging: true
    save_intermediate_results: true
```

## Integration Points

### With Existing Agents
- **Signal Agent**: Uses for signal generation and validation
- **Backtesting Agent**: Uses for comprehensive strategy evaluation
- **Performance Analysis Agent**: Can integrate for advanced metrics

### With External Systems
- **Database sync**: Optional sync with external databases
- **Webhook notifications**: Alert external systems of evolution progress
- **API integration**: REST API for external control (configurable)

## Future Enhancements

### Planned Features
- Reinforcement learning integration
- Advanced pattern recognition
- Real-time market data integration
- Distributed computing support
- Advanced visualization dashboard

### Extensibility
The agent is designed for easy extension:
- Plugin architecture for new optimization algorithms
- Configurable fitness functions
- Custom market regime detectors
- Additional storage backends

## License and Support

This enhanced strategy evolution agent addresses all the key enhancement points identified in the error.txt file and provides a robust, scalable solution for continuous strategy improvement.

For support or questions, refer to the comprehensive logging and monitoring capabilities built into the system.
